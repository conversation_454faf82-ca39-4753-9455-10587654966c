import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithSession } from '@/lib/middleware/enhanced-auth'
import { checkSessionInvalidationEvents } from '@/lib/utils/session-broadcast'

/**
 * GET /api/auth/session-events
 * Check for pending session invalidation events for the current user
 * This endpoint is used for real-time session monitoring
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const authResult = await authenticateWithSession(request)

    // Check for pending session invalidation events
    const events = checkSessionInvalidationEvents(authResult.id)

    if (events.length > 0) {
      // User has pending session invalidation events
      return NextResponse.json({
        hasInvalidationEvents: true,
        events,
        message: 'Session has been invalidated'
      }, { status: 401 })
    }

    // No invalidation events, session is still valid
    return NextResponse.json({
      hasInvalidationEvents: false,
      message: 'Session is valid'
    })

  } catch (error) {
    console.error('Session events check failed:', error)
    
    // If authentication fails, return unauthorized
    return NextResponse.json({
      hasInvalidationEvents: true,
      error: 'Authentication failed',
      message: 'Session is invalid'
    }, { status: 401 })
  }
}
