/**
 * Application configuration
 *
 * This file centralizes all configuration values from environment variables.
 * It ensures that required environment variables are present and provides
 * type-safe access to them throughout the application.
 */

// Environment
export const NODE_ENV = process.env.NODE_ENV || 'development'
export const IS_DEVELOPMENT = NODE_ENV === 'development'
export const IS_PRODUCTION = NODE_ENV === 'production'
export const IS_TEST = NODE_ENV === 'test'
export const DOMAIN = process.env.DOMAIN || 'localhost'
export const STUDENT_DOMAIN = process.env.STUDENT_DOMAIN || 'shalatyuk.libstudio.my.id'
export const ADMIN_DOMAIN = process.env.ADMIN_DOMAIN || 'adminshalat.libstudio.my.id'

// School Configuration
export const SCHOOL_NAME = process.env.SCHOOL_NAME || 'SMK Negeri 3 Banjarmasin'
export const SCHOOL_ADDRESS =
  process.env.SCHOOL_ADDRESS || 'Jl. A. <PERSON>i <PERSON>. 6, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>'
export const SCHOOL_WEBSITE = process.env.SCHOOL_WEBSITE || 'https://smkn3banjarmasin.sch.id/'

// N8N Webhook
export const N8N_WHATSAPP_WEBHOOK_URL = process.env.N8N_WHATSAPP_WEBHOOK_URL

// Database
export const DATABASE_URL =
  process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/shalat_yuk'
if (!DATABASE_URL && IS_PRODUCTION) {
  console.error('WARNING: DATABASE_URL environment variable is missing in production')
}

// Redis
export const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379'
if (!REDIS_URL && IS_PRODUCTION) {
  console.error('WARNING: REDIS_URL environment variable is missing in production')
}

// Authentication
export const JWT_SECRET = process.env.JWT_SECRET || 'development-secret-key-replace-in-production'
if (!JWT_SECRET && IS_PRODUCTION) {
  console.error('WARNING: JWT_SECRET environment variable is missing in production')
}

// Legacy environment variables (removed - no longer needed)
// NextAuth and Google OAuth have been removed from the system

// Server-side configuration object
export const serverConfig = {
  database: {
    url: DATABASE_URL,
  },
  redis: {
    url: REDIS_URL,
  },
  auth: {
    jwtSecret: JWT_SECRET,
  },
  environment: {
    nodeEnv: NODE_ENV,
    isDevelopment: IS_DEVELOPMENT,
    isProduction: IS_PRODUCTION,
    isTest: IS_TEST,
    domain: DOMAIN,
    studentDomain: STUDENT_DOMAIN,
    adminDomain: ADMIN_DOMAIN,
  },
  webhooks: {
    n8nWhatsapp: N8N_WHATSAPP_WEBHOOK_URL,
  },
  school: {
    name: SCHOOL_NAME,
    address: SCHOOL_ADDRESS,
    website: SCHOOL_WEBSITE,
  },
}

// Client-side safe configuration object (no secrets)
export const clientConfig = {
  environment: {
    nodeEnv: NODE_ENV,
    isDevelopment: IS_DEVELOPMENT,
    isProduction: IS_PRODUCTION,
    isTest: IS_TEST,
    domain: DOMAIN,
    studentDomain: STUDENT_DOMAIN,
    adminDomain: ADMIN_DOMAIN,
  },
  school: {
    name: SCHOOL_NAME,
    address: SCHOOL_ADDRESS,
    website: SCHOOL_WEBSITE,
  },
}
