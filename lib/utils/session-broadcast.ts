/**
 * Session broadcast utilities for real-time force logout
 * This provides mechanisms to notify clients about session invalidation
 */

interface SessionInvalidationEvent {
  type: 'SESSION_INVALIDATED'
  userId: number
  sessionId?: string
  timestamp: number
  reason: 'force_logout' | 'admin_action' | 'expired'
}

// In-memory store for session invalidation events
// In production, this should be replaced with Redis pub/sub or similar
const sessionInvalidationEvents = new Map<string, SessionInvalidationEvent>()

/**
 * Broadcast session invalidation event
 */
export function broadcastSessionInvalidation(
  userId: number,
  sessionId?: string,
  reason: 'force_logout' | 'admin_action' | 'expired' = 'force_logout'
): void {
  const event: SessionInvalidationEvent = {
    type: 'SESSION_INVALIDATED',
    userId,
    sessionId,
    timestamp: Date.now(),
    reason
  }

  // Store the event with a key that includes userId and timestamp
  const eventKey = `${userId}_${event.timestamp}`
  sessionInvalidationEvents.set(eventKey, event)

  // Clean up old events (older than 5 minutes)
  const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
  for (const [key, storedEvent] of sessionInvalidationEvents.entries()) {
    if (storedEvent.timestamp < fiveMinutesAgo) {
      sessionInvalidationEvents.delete(key)
    }
  }

  console.log(`Session invalidation broadcasted for user ${userId}:`, event)
}

/**
 * Check if user has pending session invalidation events
 */
export function checkSessionInvalidationEvents(userId: number): SessionInvalidationEvent[] {
  const userEvents: SessionInvalidationEvent[] = []
  
  for (const [key, event] of sessionInvalidationEvents.entries()) {
    if (event.userId === userId) {
      userEvents.push(event)
      // Remove the event after checking to prevent duplicate notifications
      sessionInvalidationEvents.delete(key)
    }
  }

  return userEvents
}

/**
 * Clear all session invalidation events for a user
 */
export function clearSessionInvalidationEvents(userId: number): void {
  for (const [key, event] of sessionInvalidationEvents.entries()) {
    if (event.userId === userId) {
      sessionInvalidationEvents.delete(key)
    }
  }
}

/**
 * Get all pending session invalidation events (for debugging)
 */
export function getAllSessionInvalidationEvents(): SessionInvalidationEvent[] {
  return Array.from(sessionInvalidationEvents.values())
}
