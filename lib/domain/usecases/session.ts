import {
  SessionData,
  CreateSessionDTO,
  SessionSummary,
  SessionFilter,
  SessionValidationResult,
} from '../entities/session'
import { SessionRepository } from '../repositories/session-repository'
import { Student } from '../entities/student'
import { Admin } from '../entities/admin'
import { NotFoundError, AuthenticationError, ValidationError } from '../errors'

/**
 * Interface for student repository (for session use cases)
 */
export interface StudentRepository {
  findById(id: number): Promise<Student | null>
}

/**
 * Interface for admin repository (for session use cases)
 */
export interface AdminRepository {
  findById(id: number): Promise<Admin | null>
}

/**
 * Interface for cache service (for session use cases)
 */
export interface CacheService {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttlSeconds: number): Promise<void>
  del(key: string): Promise<void>
}

/**
 * Session management use cases
 * Handles business logic for session creation, validation, and management
 */
export class SessionUseCases {
  constructor(
    private sessionRepo: SessionRepository,
    private studentRepo: StudentRepository,
    private adminRepo: AdminRepository,
    private cache: CacheService
  ) {}

  /**
   * Create a new session for a user
   * Enforces single-device policy by invalidating existing sessions for the same device
   */
  async createSession(sessionData: CreateSessionDTO): Promise<SessionData> {
    // Validate user exists
    await this.validateUserExists(sessionData.userId, sessionData.role)

    console.log(
      `Creating session for user ${sessionData.userId} with device ${sessionData.deviceId}`
    )

    // Check for existing session on this device
    const existingSession = await this.sessionRepo.getSessionByUserAndDevice(
      sessionData.userId,
      sessionData.deviceId
    )

    // If there's an existing session on this device, invalidate it first
    if (existingSession) {
      console.log(
        `Found existing session ${existingSession.sessionId} for device ${sessionData.deviceId}, invalidating...`
      )
      await this.sessionRepo.invalidateSession(existingSession.sessionId)

      // Wait a moment for invalidation to complete
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // Also invalidate any other sessions for this user to enforce single session per user
    // This is more strict but ensures no duplicate sessions
    const allUserSessions = await this.sessionRepo.getActiveSessionsForUser(sessionData.userId)
    for (const session of allUserSessions) {
      if (session.sessionId !== existingSession?.sessionId) {
        console.log(
          `Invalidating other session ${session.sessionId} for user ${sessionData.userId}`
        )
        await this.sessionRepo.invalidateSession(session.sessionId)
      }
    }

    // Create new session
    const newSession = await this.sessionRepo.createSession(sessionData)
    console.log(`Created new session ${newSession.sessionId} for user ${sessionData.userId}`)

    return newSession
  }

  /**
   * Validate a session and optionally refresh it
   */
  async validateSession(
    sessionId: string,
    refreshIfValid: boolean = true
  ): Promise<SessionValidationResult> {
    return await this.sessionRepo.validateSession(sessionId, refreshIfValid)
  }

  /**
   * Validate session and refresh JWT token if needed
   */
  async validateAndRefreshSession(
    sessionId: string,
    userId: number
  ): Promise<{ isValid: boolean; newToken?: string; session?: SessionData }> {
    const validation = await this.sessionRepo.validateSession(sessionId, true)

    if (!validation.isValid || !validation.session) {
      return { isValid: false }
    }

    // Check if session needs token refresh (within 10 minutes of expiry)
    const now = new Date()
    const tenMinutesFromNow = new Date(now.getTime() + 10 * 60 * 1000)

    if (validation.session.expiresAt <= tenMinutesFromNow) {
      // For now, we'll just indicate that a refresh is needed
      // The actual token generation should be handled by the auth layer
      return {
        isValid: true,
        session: validation.session,
        newToken: 'REFRESH_NEEDED', // Placeholder - actual token generation happens in auth layer
      }
    }

    return {
      isValid: true,
      session: validation.session,
    }
  }

  /**
   * Get session details by session ID
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    return await this.sessionRepo.getSession(sessionId)
  }

  /**
   * Get all active sessions for a user
   */
  async getUserSessions(userId: number): Promise<SessionData[]> {
    return await this.sessionRepo.getActiveSessionsForUser(userId)
  }

  /**
   * Invalidate a specific session
   */
  async invalidateSession(sessionId: string): Promise<boolean> {
    return await this.sessionRepo.invalidateSession(sessionId)
  }

  /**
   * Force logout a user from all devices
   */
  async forceLogoutUser(userId: number): Promise<number> {
    return await this.sessionRepo.forceLogoutUser(userId)
  }

  /**
   * List sessions with filtering (super_admin only)
   */
  async listSessions(filter?: SessionFilter, requestingUserId?: number): Promise<SessionSummary[]> {
    // Verify requesting user is super_admin
    if (requestingUserId) {
      await this.validateSuperAdminAccess(requestingUserId)
    }

    const sessions = await this.sessionRepo.listSessions(filter)

    // Enrich sessions with user names
    return await this.enrichSessionsWithUserNames(sessions)
  }

  /**
   * Get session statistics (super_admin only)
   */
  async getSessionStats(requestingUserId: number): Promise<{
    totalSessions: number
    activeSessions: number
    expiredSessions: number
    sessionsByRole: Record<string, number>
  }> {
    // Verify requesting user is super_admin
    await this.validateSuperAdminAccess(requestingUserId)

    return await this.sessionRepo.getSessionStats()
  }

  /**
   * Force logout a user by admin (super_admin only)
   */
  async adminForceLogoutUser(targetUserId: number, requestingUserId: number): Promise<number> {
    // Verify requesting user is super_admin
    await this.validateSuperAdminAccess(requestingUserId)

    // Prevent super_admin from logging out themselves
    if (targetUserId === requestingUserId) {
      throw new ValidationError('Cannot force logout yourself')
    }

    return await this.sessionRepo.forceLogoutUser(targetUserId)
  }

  /**
   * Invalidate specific session by admin (super_admin only)
   */
  async adminInvalidateSession(
    sessionId: string,
    requestingUserId: number,
    currentSessionId?: string
  ): Promise<boolean> {
    // Verify requesting user is super_admin
    await this.validateSuperAdminAccess(requestingUserId)

    // Get session details
    const session = await this.sessionRepo.getSession(sessionId)
    if (!session) {
      throw new NotFoundError('Session not found')
    }

    // Prevent admin from invalidating any of their own sessions
    // This ensures admin cannot accidentally logout themselves
    if (session.userId === requestingUserId) {
      throw new ValidationError('Cannot invalidate your own sessions')
    }

    return await this.sessionRepo.invalidateSession(sessionId)
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    return await this.sessionRepo.cleanupExpiredSessions()
  }

  /**
   * Get user's device sessions
   */
  async getUserDeviceSessions(userId: number): Promise<
    Array<{
      deviceId: string
      sessionId: string
      lastAccessedAt: Date
      deviceType?: string
      browser?: string
    }>
  > {
    return await this.sessionRepo.getUserDeviceSessions(userId)
  }

  /**
   * Validate that a user exists
   */
  private async validateUserExists(
    userId: number,
    role: 'student' | 'admin' | 'super_admin'
  ): Promise<void> {
    if (role === 'student') {
      const student = await this.studentRepo.findById(userId)
      if (!student) {
        throw new NotFoundError('Student not found')
      }
    } else {
      const admin = await this.adminRepo.findById(userId)
      if (!admin) {
        throw new NotFoundError('Admin not found')
      }
    }
  }

  /**
   * Validate that the requesting user is a super_admin
   */
  private async validateSuperAdminAccess(userId: number): Promise<void> {
    const admin = await this.adminRepo.findById(userId)
    if (!admin || admin.role !== 'super_admin') {
      throw new AuthenticationError('Super admin access required')
    }
  }

  /**
   * Enrich session summaries with user names
   */
  private async enrichSessionsWithUserNames(sessions: SessionSummary[]): Promise<SessionSummary[]> {
    const enrichedSessions: SessionSummary[] = []

    for (const session of sessions) {
      let userName = 'Unknown User'

      try {
        if (session.role === 'student') {
          const student = await this.studentRepo.findById(session.userId)
          userName = student?.name || 'Unknown Student'
        } else {
          const admin = await this.adminRepo.findById(session.userId)
          userName = admin?.name || 'Unknown Admin'
        }
      } catch (error) {
        console.error(`Error fetching user name for session ${session.sessionId}:`, error)
      }

      enrichedSessions.push({
        ...session,
        userName,
      })
    }

    return enrichedSessions
  }
}
